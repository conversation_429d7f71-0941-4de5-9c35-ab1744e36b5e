import { expo } from "@better-auth/expo";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";

import prisma from "prisma/db";

export const auth = betterAuth({
  database: prismaAdapter(prisma, { provider: "postgresql" }),
  // allow your dev app/web origins; add your LAN IP when testing on device
  trustedOrigins: [
    "http://localhost:19006", // Expo web
    "myapp://", // Expo deep link scheme (see step 3)
  ],
  emailAndPassword: { enabled: true }, // simple auth to start
  plugins: [expo()], // enable Expo flows on the server
});
